import { Check, Close } from '@mui/icons-material';
import type { GridColType, GridRenderCellParams } from '@mui/x-data-grid';
import { isNullish } from 'common';
import { match } from 'ts-pattern';
import { z } from 'zod';

const BooleanCell = ({ value, colDef }: GridRenderCellParams) => {
  if (isNullish(value)) return '';
  if (typeof value !== 'boolean') console.error(`Value must be boolean: ${colDef.field}`);
  const Icon = value ? Check : Close;
  return <Icon sx={{ color: (t) => t.palette.text.secondary }} />;
};

const SingleSelectCell = ({ value }: GridRenderCellParams) => {
  if (isNullish(value)) return '';
  if (Array.isArray(value)) return z.array(z.string()).parse(value).join(', ');
  return value;
};

export const getDefaultCellComponent = (type: GridColType | null | undefined) =>
  match(type)
    .with('boolean', () => BooleanCell)
    .with('singleSelect', () => SingleSelectCell)
    .otherwise(() => undefined);
