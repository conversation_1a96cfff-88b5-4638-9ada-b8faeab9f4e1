import React from 'react';

import { Close as CloseIcon, KeyboardArrowLeft, KeyboardArrowRight } from '@mui/icons-material';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  IconButton,
  MobileStepper,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from '@mui/material';

import type { DuplicatedPostalCode, SelectedPostalCode } from './types';

type Props = {
  open: boolean;
  onClose: () => void;
  onComplete: (selected: SelectedPostalCode[]) => void;
  duplicated: DuplicatedPostalCode[];
};

export const PostalCodeSelectionDialog = ({ open, onClose, onComplete, duplicated }: Props) => {
  if (duplicated.length === 0) return null;
  const [stepIndex, setStepIndex] = React.useState(0);
  const [selected, setSelected] = React.useState<SelectedPostalCode[]>(() =>
    duplicated.map((d) => ({ id: d.id, code: '' })),
  );
  const [manualInputs, setManualInputs] = React.useState<string[]>(() => duplicated.map(() => ''));

  const handleNext = () => setStepIndex((prevIndex) => prevIndex + 1);
  const handleBack = () => setStepIndex((prevIndex) => prevIndex - 1);

  const handleCodeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSelected((s) =>
      s.map((item, index) => {
        if (index !== stepIndex) return item;
        if (value !== 'no-match') {
          return { ...item, code: value };
        }
        // When "no-match" is selected, use manual input if available, otherwise null
        const manualInput = manualInputs[index];
        return { ...item, code: manualInput || null };
      }),
    );
  };

  const handleManualInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = event.target.value;
    // Only allow digits and limit to 7 characters
    const value = rawValue.replace(/[^0-9]/g, '').slice(0, 7);

    setManualInputs((inputs) =>
      inputs.map((input, index) => (index === stepIndex ? value : input)),
    );

    // Update selected state if "no-match" is currently selected
    const currentRadioValue =
      selected?.[stepIndex]?.code === null ? 'no-match' : selected?.[stepIndex]?.code || '';
    if (currentRadioValue === 'no-match') {
      setSelected((s) =>
        s.map((item, index) => {
          if (index !== stepIndex) return item;
          return { ...item, code: value || null };
        }),
      );
    }
  };

  const handleComplete = () => {
    onComplete(selected);
    onClose();
  };

  const handleCancel = () => {
    onComplete([]);
    onClose();
  };

  const current = duplicated[stepIndex];

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        郵便番号選択
        <IconButton
          aria-label="close"
          onClick={handleCancel}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.text.grey,
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body2" color="warning">
          {`住所から郵便番号を特定できない台数: ${duplicated.length}台`}
        </Typography>
        <MobileStepper
          variant="text"
          steps={duplicated.length}
          position="static"
          activeStep={stepIndex}
          sx={{ flexGrow: 1, mb: 2 }}
          backButton={
            <Button
              startIcon={<KeyboardArrowLeft />}
              size="small"
              onClick={handleBack}
              disabled={stepIndex === 0}
            >
              戻る
            </Button>
          }
          nextButton={
            <Button
              endIcon={<KeyboardArrowRight />}
              size="small"
              onClick={handleNext}
              disabled={stepIndex === duplicated.length - 1 || selected?.[stepIndex]?.code === ''}
            >
              次へ
            </Button>
          }
        />

        <Typography variant="subtitle1" gutterBottom>
          住所: {current.address}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          複数の候補となる郵便番号があります。該当するものを選択してください。
        </Typography>

        <FormControl component="fieldset" sx={{ width: '100%' }}>
          <RadioGroup
            aria-label="postal-code-options"
            name="postal-code-options"
            value={
              selected?.[stepIndex]?.code === null ? 'no-match' : selected?.[stepIndex]?.code || ''
            }
            onChange={handleCodeChange}
          >
            {current.options.map((option) => (
              <FormControlLabel
                key={option.code}
                value={option.code}
                control={<Radio />}
                label={`${option.code} - ${option.townAreaRaw}`}
              />
            ))}
            <FormControlLabel
              value="no-match"
              control={<Radio />}
              label="該当する郵便番号が見つかりません"
            />
          </RadioGroup>

          {/* Manual postal code input field - only show when "no-match" is selected */}
          {(selected?.[stepIndex]?.code === null
            ? 'no-match'
            : selected?.[stepIndex]?.code || '') === 'no-match' && (
            <TextField
              fullWidth
              size="small"
              placeholder="郵便番号を入力してください"
              value={manualInputs[stepIndex] || ''}
              onChange={handleManualInputChange}
              sx={{ mt: 2, ml: 4 }}
              helperText="例: 1234567 (7桁の数字)"
            />
          )}
        </FormControl>
      </DialogContent>
      <DialogActions>
        <Button
          variant="contained"
          onClick={handleComplete}
          disabled={selected?.some((item) => item.code === '')}
        >
          完了
        </Button>
      </DialogActions>
    </Dialog>
  );
};
